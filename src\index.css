@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

body {
  margin: 0;
  height: 100vh;
  overflow: hidden;
  background-color: #0f1419;
  color: white;
  font-family: Inter, system-ui, sans-serif;
}

#root {
  height: 100vh;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 1920x1080 比例缩放容器 */
.scale-container {
  width: 1920px;
  height: 1080px;
  transform-origin: center center;
  position: relative;
  background-color: #081222;
}

/* 自动计算缩放比例 */
.auto-scale {
  --scale-x: calc(100vw / 1920);
  --scale-y: calc(100vh / 1080);
  --scale: min(var(--scale-x), var(--scale-y));
  transform: scale(var(--scale));
  transition: transform 0.3s ease;
}

/* 防止过度缩小 */
@media (max-width: 960px) or (max-height: 540px) {
  .auto-scale {
    --min-scale: 0.5;
    --scale: max(var(--min-scale), min(var(--scale-x), var(--scale-y)));
  }
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  a:hover {
    color: #747bff;
  }

  button {
    background-color: #f9f9f9;
  }
}

/* 自定义工具类 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 固定网格布局 - 基于1920x1080设计 */
.seat-grid {
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

/* 滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #718096;
}
