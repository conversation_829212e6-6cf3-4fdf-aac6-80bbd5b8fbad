.modalWrapper :global(.ant-modal-content) {
  background-color: #0d1e3c;
  color: white;
  border-radius: 8px;
}

.container {
  padding: 20px;
}

.topRow {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
}

.selectBox,
.inputBox {
  width: 200px;
}

.vs {
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.section {
  margin: 16px 0;
}

.label {
  color: white;
  margin-bottom: 4px;
}

.textArea {
  width: 100%;
  background-color: #16325e;
  color: white;
  box-shadow: 0px 4px 4px 0px #0000000d;
  border: 1px solid #ffffff1a;
}

.tabs :global(.ant-tabs-tab) {
  color: white;
}
.tabs :global(.ant-tabs-tab-active) {
  font-weight: bold;
}

.indicatorRow {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
}

.indicatorInput {
  flex: 1;
  background-color: #1d2b4d;
  color: white;
}

.addButton {
  background-color: #2a4d9f;
  color: white;
}

.opponentInput {
  width: 100%;
  margin-bottom: 12px;
  background-color: #1d2b4d;
  color: white;
}

.customTable :global(.ant-table) {
  background-color: transparent;
  color: white;
}

.customTable :global(.ant-table-thead > tr > th) {
  background-color: #16233e;
  color: white;
}

.customTable :global(.ant-table-tbody > tr > td) {
  background-color: #1a2a4d;
  color: white;
}

.footer {
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btnGroup {
  display: flex;
  gap: 8px;
}

.btn {
  background-color: #2a81ff;
  color: white;
}
