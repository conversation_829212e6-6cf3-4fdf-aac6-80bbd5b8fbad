import React from "react"
import { SeatConfiguration, PositionOption } from "@/types"
import SeatCard from "./SeatCard"

interface SeatGridProps {
  seatConfig: SeatConfiguration
  positionOptions: PositionOption[]
  onSelectCharacter: (seatId: string) => void
  onPositionChange: (seatId: string, position: string) => void
  onCreateInfo: (seatId: string) => void
  onViewInfo: (seatId: string) => void
}

const SeatGrid: React.FC<SeatGridProps> = ({
  seatConfig,
  positionOptions,
  onSelectCharacter,
  onPositionChange,
  onCreateInfo,
  onViewInfo,
}) => {
  return (
    <div className="w-full">
      {/* 3x3 网格布局 */}
      <div className="seat-grid mx-auto grid gap-4 sm:gap-6">
        {seatConfig.seats.map(seat => (
          <SeatCard
            key={seat.id}
            seat={seat}
            positionOptions={positionOptions}
            onSelectCharacter={() => onSelectCharacter(seat.id)}
            onPositionChange={position => onPositionChange(seat.id, position)}
            onCreateInfo={() => onCreateInfo(seat.id)}
            onViewInfo={() => onViewInfo(seat.id)}
          />
        ))}
      </div>
    </div>
  )
}

export default SeatGrid
