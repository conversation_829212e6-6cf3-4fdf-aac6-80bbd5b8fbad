/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  /* 滚动条宽度 */
  height: 8px;
  /* 水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  /* 滚动条轨道背景 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  /* 滚动条滑块颜色 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
  /* 滑块悬停时的颜色 */
}
.ant-input-outlined:hover {
  background-color: #16325e !important;
}
.ant-input-outlined:focus {
  border-color: #295db0 !important;
  background-color: #16325e;
}
.ant-input-outlined {
  border: none;
}
.ant-input {
  background: #1b3d75 !important;
  color: white !important;
}
.ant-form-item .ant-form-item-label > label {
  color: white !important;
}
.ant-input-outlined.ant-input-status-error {
  background-color: transparent !important;
}

.ant-input::placeholder {
  color: #898989;
}
.ant-btn-variant-outlined {
  border-color: #295db0;
}
button:focus {
  outline: none;
}

/* select */
.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background: #3d66a7 !important;
}
.ant-select-dropdown {
  background: #132b51 !important;
}
.ant-select-item-option-content {
  color: #ffffff !important;
}
.ant-select .ant-select-arrow {
  color: #ffffff !important;
}
.ant-select-outlined .ant-select-selector {
  background: #1b3d75 !important;
  border-color: transparent !important;
  color: #ffffff !important;
}
.ant-select-single.ant-select-open .ant-select-selection-item {
  color: #ffffff !important;
}
.ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
  border-color: transparent !important;
}

.ant-select .ant-select-selection-placeholder {
  color: #afafaf;
}

/* Button */
.ant-btn-variant-outlined:disabled,
.ant-btn-variant-solid:disabled {
  background: #2a81ff;
  color: #fff;
  border: none;
}

/*TextArea */
