import { useEffect, useRef } from "react"

export function useAutoScrollToBottom(
  ref: React.RefObject<HTMLElement>,
  shouldScroll: boolean,
  deps: Array<string | number | boolean | object | null | undefined> = [],
  threshold = 100 // 离底部多少 px 内才自动滑
) {
  const isUserNearBottomRef = useRef(true)

  useEffect(() => {
    const container = ref.current
    if (!container) return

    const handleScroll = () => {
      const distanceToBottom = container.scrollHeight - container.scrollTop - container.clientHeight
      isUserNearBottomRef.current = distanceToBottom < threshold
    }

    container.addEventListener("scroll", handleScroll)

    return () => {
      container.removeEventListener("scroll", handleScroll)
    }
  }, [ref])

  useEffect(() => {
    const container = ref.current
    if (!container || !shouldScroll) return

    if (isUserNearBottomRef.current) {
      requestAnimationFrame(() => {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: "smooth",
        })
      })
    }
  }, [shouldScroll, ...deps])
}
