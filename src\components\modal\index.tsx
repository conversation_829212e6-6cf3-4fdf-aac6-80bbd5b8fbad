import React from "react"

interface BaseModalProps {
  isOpen: boolean
  title?: string
  children: React.ReactNode
  onConfirm?: () => void
  onCancel?: () => void
  confirmText?: string
  cancelText?: string
  showFooter?: boolean
  confirmDisabled?: boolean
  headerRightSlot?: React.ReactNode
  width?: string | number
  height?: string | number
}

const BaseModal: React.FC<BaseModalProps> = ({
  isOpen,
  title,
  children,
  onConfirm,
  onCancel,
  confirmText = "确认",
  cancelText = "关闭",
  showFooter = false,
  confirmDisabled = false,
  headerRightSlot,
  width = "100%", // 默认宽高
  height = "750px",
}) => {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 遮罩 */}
      <div
        className="absolute inset-0 bg-black bg-opacity-70 transition-opacity duration-300"
        onClick={onCancel}
      />

      {/* 弹窗本体 */}
      <div
        className="relative h-[750px] w-full max-w-[1440px] scale-100 transform rounded-[20px] px-[85px] opacity-100 shadow-lg transition-all duration-300"
        style={{
          backgroundColor: "#132B51",
          boxShadow: "0px 4px 4px 0px #00000040",
          width,
          height,
          maxWidth: "1440px",
        }}
      >
        {/* 头部 */}
        {(title || headerRightSlot) && (
          <div className="flex items-center justify-between border-b border-gray-600 p-6">
            {title && (
              <h2 className="mr-[66px] whitespace-nowrap text-[26px] font-semibold text-white">
                {title}
              </h2>
            )}
            <div className="flex-1">{headerRightSlot}</div>
          </div>
        )}

        {/* 内容 */}
        <div className="max-h-[calc(100%-160px)] overflow-y-auto p-4">{children}</div>

        {/* 底部 */}
        {showFooter && (
          <div className="flex items-center justify-center space-x-4 border-t border-gray-600 p-4">
            <button
              onClick={onConfirm}
              disabled={confirmDisabled}
              className={`rounded-lg px-6 py-2 font-medium transition-all duration-300 ${
                confirmDisabled
                  ? "cursor-not-allowed bg-gray-600 text-gray-400"
                  : "active:scale-98 cursor-pointer bg-[#2A81FF] text-white hover:scale-105 hover:bg-blue-600 hover:shadow-lg hover:shadow-blue-500/30"
              }`}
            >
              {confirmText}
            </button>
            <button
              onClick={onCancel}
              className="active:scale-98 rounded-lg bg-gray-700 px-6 py-2 text-white transition-all duration-300 hover:scale-105 hover:bg-gray-600 hover:shadow-lg"
            >
              {cancelText}
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default BaseModal
