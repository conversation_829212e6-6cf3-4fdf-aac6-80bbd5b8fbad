import React, { useState, useEffect, useMemo } from "react"
import { Character, PositionOption, Seat, SeatConfiguration } from "../../types"
import { Input, message } from "antd"
import SeatGrid from "./components/SeatGrid"
import { generateMockCharacters } from "./utils/mockData"
import CharacterSelectionModal from "./components/CharacterSelectionModal"
import InfoModal, { FieldType } from "./components/InfoModal"
import { useNavigate } from "react-router-dom"

const Characters: React.FC = () => {
  const [seatCount, setSeatCount] = useState<number | null>(null)
  const [isEmpty, setIsEmpty] = useState(true)
  const [seatConfig, setSeatConfig] = useState<SeatConfiguration>({
    totalSeats: 0,
    seats: [],
  })
  const [selectedSeatId, setSelectedSeatId] = useState<string | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [infoModalOpen, setInfoModalOpen] = useState(false)
  const [currentSeatForInfo, setCurrentSeatForInfo] = useState<string | null>(null)
  const [isInfoViewMode, setIsInfoViewMode] = useState(false)

  const canConfirm = useMemo(() => {
    return seatConfig.seats.every(seat => seat.character && seat.seatInfo)
  }, [seatConfig.seats])

  const navigate = useNavigate()

  // 处理席位数量变化
  useEffect(() => {
    if (seatCount && seatCount >= 1 && seatCount <= 9) {
      const seats: Seat[] = Array.from({ length: seatCount }, (_, i) => ({
        id: `seat-${i}`,
        position: i,
        type: i === 0 ? "decision" : "general",
        character: undefined,
        position_title: undefined,
      }))

      setSeatConfig({
        totalSeats: seatCount,
        seats,
      })
      setIsEmpty(false)
    } else {
      setSeatConfig({
        totalSeats: 0,
        seats: [],
      })
      setIsEmpty(true)
    }
  }, [seatCount])

  const positionOptions: PositionOption[] = [
    { value: "president", label: "总统" },
    { value: "foreign_minister", label: "外交部长" },
    { value: "defense_minister", label: "国防部长" },
  ]

  const handleSelectCharacter = (seatId: string) => {
    setSelectedSeatId(seatId)
    setIsModalOpen(true)
  }

  const handleCreateInfo = (seatId: string) => {
    setCurrentSeatForInfo(seatId)
    setIsInfoViewMode(false)
    setInfoModalOpen(true)
  }

  const handleViewInfo = (seatId: string) => {
    setCurrentSeatForInfo(seatId)
    setIsInfoViewMode(true)
    setInfoModalOpen(true)
  }

  const handleConfirmCharacterSelection = (character: Character) => {
    if (selectedSeatId) {
      setSeatConfig(prev => ({
        ...prev,
        seats: prev.seats.map(seat => (seat.id === selectedSeatId ? { ...seat, character } : seat)),
      }))
    }
    setIsModalOpen(false)
    setSelectedSeatId(null)
  }

  const handleInfo = (formData: FieldType) => {
    if (currentSeatForInfo) {
      // 将FieldType转换为SeatInfo
      const seatInfo = {
        position_focus: formData.position_focus,
        position_title: formData.position_title,
        decision_making_style: formData.decision_making_style,
        position_id: formData.position_id,
        isCustomPosition: formData.isCustomPosition,
      }

      setSeatConfig(prev => ({
        ...prev,
        seats: prev.seats.map(seat =>
          seat.id === currentSeatForInfo ? { ...seat, seatInfo } : seat
        ),
      }))
    }
    setInfoModalOpen(false)
    setCurrentSeatForInfo(null)
    setIsInfoViewMode(false)
  }

  const handlePositionChange = (seatId: string, position: string) => {
    setSeatConfig(prev => ({
      ...prev,
      seats: prev.seats.map(seat =>
        seat.id === seatId ? { ...seat, position_title: position } : seat
      ),
    }))
  }

  const handleConfirm = async () => {
    if (canConfirm) {
      try {
        // 准备发送给后端的数据
        const seatData = seatConfig.seats.map(seat => ({
          id: seat.id,
          position: seat.position,
          type: seat.type,
          character: seat.character,
          seatInfo: seat.seatInfo,
        }))

        console.log("发送给后端的数据:", seatData)

        // TODO: 这里调用实际的API
        // await submitSeatConfiguration(seatData)
        navigate("/cardDiscussion")

        message.success("保存成功")
      } catch (error) {
        console.error("保存失败:", error)
        message.error("保存失败，请重试")
      }
    }
  }

  const renderEmptyState = () => (
    <div className="flex h-full flex-col items-center justify-center text-gray-400">
      <div className="mb-4 h-32 w-32 opacity-30">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
          />
        </svg>
      </div>
      <p className="text-lg">请输入1-9之间的席位数量</p>
    </div>
  )

  return (
    <div className="flex flex-col">
      <div className="flex min-h-[850px] flex-col">
        <div className="flex h-[100px] w-full flex-shrink-0 items-center rounded-t-3xl bg-[#2657A4] px-4 py-2 text-white">
          <span className="ml-4 text-[26px] font-black">请输入席位数量</span>
          <Input
            placeholder="请输入1-9数字"
            className="!focus:bg-[#16325E] ml-4 h-9 w-36 rounded-md border-none bg-[#16325E] px-4 py-2 text-white hover:bg-[#16325E]"
            value={seatCount || ""}
            onChange={e => {
              const filteredValue = e.target.value.replace(/[^1-9]/g, "")
              setSeatCount(filteredValue ? Number(filteredValue) : null)
            }}
            maxLength={1}
            pattern="[1-9]+"
          />
          <span className="ml-4 text-[26px]">当前已选{seatConfig.seats.length}个角色</span>
        </div>

        <div
          className={`flex w-full flex-1 flex-col items-center rounded-b-3xl bg-[#132B51] p-12 pb-9 ${
            isEmpty ? "justify-center" : "justify-start"
          }`}
        >
          {isEmpty ? (
            renderEmptyState()
          ) : (
            <SeatGrid
              seatConfig={seatConfig}
              positionOptions={positionOptions}
              onSelectCharacter={handleSelectCharacter}
              onPositionChange={handlePositionChange}
              onCreateInfo={handleCreateInfo}
              onViewInfo={handleViewInfo}
            />
          )}
        </div>

        <CharacterSelectionModal
          isOpen={isModalOpen}
          onConfirm={handleConfirmCharacterSelection}
          onClose={() => {
            setIsModalOpen(false)
            setSelectedSeatId(null)
          }}
        />
        <InfoModal
          isOpen={infoModalOpen}
          onClose={() => {
            setInfoModalOpen(false)
            setCurrentSeatForInfo(null)
            setIsInfoViewMode(false)
          }}
          onInfoConfirm={handleInfo}
          initialData={
            currentSeatForInfo
              ? (() => {
                  const seatInfo = seatConfig.seats.find(
                    seat => seat.id === currentSeatForInfo
                  )?.seatInfo
                  return seatInfo
                    ? {
                        position_focus: seatInfo.position_focus,
                        position_title: seatInfo.position_title,
                        decision_making_style: seatInfo.decision_making_style,
                        position_id: seatInfo.position_id,
                        isCustomPosition: seatInfo.isCustomPosition,
                      }
                    : undefined
                })()
              : undefined
          }
          isViewMode={isInfoViewMode}
        />
      </div>
      <div className="mt-6 flex flex-1 justify-center">
        <button
          onClick={handleConfirm}
          disabled={!canConfirm}
          className={`flex h-[40px] w-[180px] items-center justify-center rounded-lg text-lg font-medium transition-all ${
            canConfirm
              ? "cursor-pointer bg-blue-600 text-white hover:bg-blue-700"
              : "cursor-not-allowed bg-gray-600 text-gray-400"
          }`}
        >
          确认
        </button>
      </div>
    </div>
  )
}

export default Characters
