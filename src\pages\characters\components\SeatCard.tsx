import React from "react"
import { <PERSON>r, CirclePlus, Eye } from "lucide-react"
import { Seat, PositionOption } from "@/types"
import { Button, Tooltip } from "antd" // 引入Ant Design的Select组件

interface SeatCardProps {
  seat: Seat
  positionOptions: PositionOption[]
  onSelectCharacter: () => void
  onPositionChange: (position: string) => void
  onCreateInfo: () => void
  onViewInfo: () => void
}

const SeatCard: React.FC<SeatCardProps> = ({
  seat,
  onSelectCharacter,
  onCreateInfo,
  onViewInfo,
}) => {
  const truncateText = (text: string, maxLength: number) => {
    return text?.length > maxLength ? text.substring(0, maxLength) + "..." : text
  }

  // 判断席位信息是否完整
  const isInfoComplete = seat.seatInfo
  // 判断是否有角色
  const hasCharacter = !!seat.character
  // 判断按钮是否禁用
  const isButtonDisabled = !hasCharacter

  // 处理信息按钮点击
  const handleInfoButtonClick = () => {
    if (isButtonDisabled) return

    if (isInfoComplete) {
      onViewInfo()
    } else {
      onCreateInfo()
    }
  }

  return (
    <div
      className="relative h-[197px] w-[480px] overflow-hidden rounded-[20px] border border-white/10 bg-[#14438E] p-[20px] transition-all duration-300 hover:-translate-y-1 hover:scale-[1.03] hover:shadow-xl hover:shadow-blue-600/30"
      style={{ transform: "translateZ(0)", perspective: "1000px" }}
    >
      {/* 悬停效果背景层 */}
      <div className="pointer-events-none absolute inset-0 bg-gradient-to-br from-blue-500/0 to-blue-500/20 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>

      {/* 席位标题 */}
      <div className="relative z-10 mb-4 flex items-center justify-between">
        <div className="flex w-full items-center justify-between space-x-2">
          <span className="text-[22px] text-white">
            {seat.type === "decision" ? "决策席位" : "一般席位"}
          </span>

          {/* 信息填写/查看按钮  */}
          <div className="relative flex items-center justify-center">
            <Tooltip title={isButtonDisabled ? "请先选择角色" : ""} placement="top">
              <Button
                onClick={handleInfoButtonClick}
                type="primary"
                disabled={isButtonDisabled}
                className={`w-full transform rounded-lg px-4 py-2 text-sm text-white transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md ${
                  isButtonDisabled
                    ? "cursor-not-allowed bg-gray-500 hover:bg-gray-500"
                    : isInfoComplete
                      ? "bg-green-600 hover:bg-green-700 hover:shadow-green-500/30"
                      : "bg-blue-600 hover:bg-blue-700 hover:shadow-blue-500/30"
                }`}
                icon={
                  isInfoComplete ? <Eye className="h-3 w-3" /> : <CirclePlus className="h-3 w-3" />
                }
              >
                {isInfoComplete ? "查看" : "信息填写"}
              </Button>
            </Tooltip>
          </div>
        </div>
      </div>

      {/* 角色信息 */}
      <div className="relative z-10 mb-4">
        {seat.character ? (
          <div className="space-y-3">
            {/* 角色头像和名称 */}
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 flex-shrink-0 transform overflow-hidden rounded-full bg-gray-600 transition-transform duration-300 group-hover:scale-110">
                {seat.character.avatar ? (
                  <img
                    src={seat.character.avatar}
                    alt={seat.character.character_name}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center">
                    <User className="h-6 w-6 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="min-w-0 flex-1">
                <h4 className="truncate font-medium text-white">
                  {truncateText(seat.character.character_name, 15)}
                </h4>
                <p className="mt-1 line-clamp-2 text-sm text-gray-400">
                  {truncateText(seat.character.character_description, 50)}
                </p>
              </div>
            </div>

            {/* 更换角色按钮 */}
            <button
              onClick={onSelectCharacter}
              className="w-full transform rounded-lg bg-blue-600 px-4 py-2 text-sm text-white transition-all duration-300 hover:-translate-y-0.5 hover:bg-blue-700 hover:shadow-md hover:shadow-blue-500/30"
            >
              更换角色
            </button>
          </div>
        ) : (
          <button
            onClick={onSelectCharacter}
            className="group h-[117px] w-full rounded-lg border-2 border-dashed border-gray-600 bg-[#FFFFFF1A] transition-all duration-300 hover:border-blue-400 hover:bg-[#FFFFFF2A] hover:shadow-lg hover:shadow-blue-500/20"
          >
            <div className="flex flex-col items-center space-y-2 pt-4">
              <CirclePlus className="h-10 w-10 text-white/60 transition-colors duration-300 group-hover:rotate-180 group-hover:scale-110 group-hover:text-white" />
              <span className="text-[22px] text-white/60 transition-colors duration-300 group-hover:text-white">
                点击选择角色
              </span>
            </div>
          </button>
        )}
      </div>
    </div>
  )
}

export default SeatCard
