import React, { useState } from "react"
import { useNavigate } from "react-router-dom"
import { create<PERSON>haracter, getPersonalityAnalysis } from "@/api/create<PERSON>haracter"
import RadarCharts from "./compoents/RadarChart"
import BarCharts from "./compoents/Barchart"
import { Button, Form, FormProps, Input, message } from "antd"
import { roleField } from "./utils"

interface PersonalityResult {
  openness: number
  neuroticism: number
  conscientiousness: number
  agreeableness: number
  extraversion: number
}

const CreateCharacter: React.FC = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    rolename: "",
    description: "",
  })
  const [loading, setLoading] = useState(false)
  const [personalityData, setPersonalityData] = useState([
    { name: "开放性", value: 0 },
    { name: "神经质", value: 0 },
    { name: "宜人性", value: 0 },
    { name: "外向性", value: 0 },
    { name: "尽责性", value: 0 },
  ])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const params = {
      character_name: formData.rolename,
      character_description: formData.description,
      openness: personalityData[0].value,
      neuroticism: personalityData[1].value,
      conscientiousness: personalityData[2].value,
      agreeableness: personalityData[3].value,
      extraversion: personalityData[4].value,
    }

    try {
      await createCharacter(params).then(res => {
        if (res.code === 200) {
          message.success(res.message || "创建成功")
          navigate("/")
        } else {
          message.error(res.message || "创建失败")
        }
      })
    } catch (error) {
      console.error("创建卡牌失败:", error)
    }
  }

  const onFinish: FormProps<roleField>["onFinish"] = async values => {
    if (!values.rolename || !values.description) return
    setFormData({
      rolename: values.rolename ?? "",
      description: values.description ?? "",
    })
    setLoading(true)
    try {
      const result: PersonalityResult = await getPersonalityAnalysis(
        values.rolename,
        values.description
      )

      setPersonalityData([
        { name: "开放性", value: result.openness },
        { name: "神经质", value: result.neuroticism },
        { name: "宜人性", value: result.agreeableness },
        { name: "外向性", value: result.extraversion },
        { name: "尽责性", value: result.conscientiousness },
      ])
    } catch (error) {
      console.error("人格分析失败:", error)
    } finally {
      setLoading(false)
    }
  }

  const onFinishFailed: FormProps<roleField>["onFinishFailed"] = errorInfo => {
    console.log("Failed:", errorInfo)
  }

  return (
    <div className="h-full">
      <div className="mx-auto flex h-[420px] w-full flex-col">
        <div className="flex h-[100px] w-full items-center rounded-t-xl bg-[#2657A4] px-4 py-2 text-white">
          <span className="ml-4 text-[32px] font-black">手动输入人物描述</span>
        </div>
        <div className="flex flex-1 rounded-b-xl bg-[#132B51]">
          <Form
            name="basic"
            labelCol={{ span: 2 }}
            wrapperCol={{ span: 20 }}
            initialValues={{ remember: true }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            style={{ width: "100%" }}
            className="mt-6"
            requiredMark={false}
          >
            <Form.Item<roleField>
              label="姓名"
              name="rolename"
              className="ml-6 text-white"
              labelAlign="left"
              rules={[{ required: true, message: "请输入姓名!" }]}
            >
              <Input
                placeholder="请输入姓名"
                className="!h-10 w-full resize-none border-[#295DB0] bg-[#09273E] text-white"
              />
            </Form.Item>
            <Form.Item<roleField>
              label="人物描述"
              name="description"
              className="ml-6 text-white"
              labelAlign="left"
              rules={[{ required: true, message: "请输入人物描述!" }]}
            >
              <Input.TextArea
                placeholder="请输入对一个人物的描述"
                className="!h-36 w-full resize-none border-[#295DB0] bg-[#09273E] p-3 pb-0 text-white"
                style={{ scrollbarWidth: "none" }}
              />
            </Form.Item>
            <Form.Item label={null} className="flex items-center justify-center">
              <Button
                type="primary"
                htmlType="submit"
                disabled={loading}
                loading={loading}
                className="z-10 flex h-10 w-48 items-center justify-center rounded-xl bg-[#2A81FF] text-white transition-colors hover:bg-[#2f86ff] disabled:opacity-50"
              >
                {loading ? "分析中..." : "分析人物性格"}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
      <div className="mx-auto mt-5 flex h-[500px] w-full flex-col">
        <div className="flex h-[100px] w-full items-center rounded-t-xl bg-[#2657A4] px-4 py-2 text-white">
          <span className="ml-4 text-[32px] font-black">五因素人格维度</span>
        </div>
        <div className="flex flex-1 items-center justify-center bg-[#132B51]">
          <RadarCharts
            data={personalityData}
            width={500}
            height="320px"
            margin="0"
            center={["40%", "55%"]}
          />
          <BarCharts data={personalityData}></BarCharts>
        </div>
        <div className="flex items-center justify-center rounded-b-xl bg-[#132B51] pb-3">
          <Button
            type="primary"
            className="z-10 flex h-10 w-48 items-center justify-center rounded-xl bg-[#2A81FF] text-white transition-colors hover:bg-[#2f86ff] disabled:opacity-50"
            onClick={handleSubmit}
          >
            保存并确认
          </Button>
        </div>
      </div>
    </div>
  )
}

export default CreateCharacter
