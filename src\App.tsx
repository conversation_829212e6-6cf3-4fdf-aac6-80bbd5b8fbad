import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import Layout from "@/components/Layout/Layout"
import Home from "@/pages/home/<USER>"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/pages/createCharacter"
import Characters from "@/pages/characters"
import Discussions from "@/pages/discussions"
import DeductionProcess from "@/pages/deductionProcess"
import CardDiscussion from "./pages/cardDiscussion"
import ScaleTest from "./pages/scale-test"
import "@/App.css"
import "@ant-design/v5-patch-for-react-19"
function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/create" element={<CreateCharacter />} />
          <Route path="/characters/:id" element={<Characters />} />
          <Route path="/discussions" element={<Discussions />} />
          <Route path="/deductionProcess" element={<DeductionProcess />} />
          <Route path="/cardDiscussion" element={<CardDiscussion />} />
          <Route path="/scale-test" element={<ScaleTest />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default App
