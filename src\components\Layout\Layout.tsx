import React from "react"
import Header from "./Header"
import ScaleDebugger from "../ScaleDebugger"

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <>
      <div className="scale-container auto-scale">
        <Header className="fixed left-0 right-0 top-0 z-10" />
        <main
          className="mt-24 overflow-hidden px-24 py-6"
          style={{ height: "calc(1080px - 96px)" }}
        >
          {children}
        </main>
      </div>
      <ScaleDebugger />
    </>
  )
}

export default Layout
