import React from "react"
import Header from "./Header"

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="flex min-h-screen flex-col bg-primary-background">
      <Header className="fixed left-0 right-0 top-0 z-10" />
      <main className="mt-24 flex-1 overflow-y-auto px-24 py-6">{children}</main>
    </div>
  )
}

export default Layout
