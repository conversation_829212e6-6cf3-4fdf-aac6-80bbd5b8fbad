import { httpClient } from '@/utils/request';
import { Character } from '@/types';


//获取人物性格分析参数 

export const getPersonalityAnalysis = async (name: string, description: string): Promise<any> => {
    return httpClient.post(`/chat/znrgfx_chat/?card_character_name=${name}&personality_analysis=${description}`);
};

export const createCharacter = async (data: Character): Promise<any> => {
    return httpClient.post(`/card_character/card_character/`, data );
};
