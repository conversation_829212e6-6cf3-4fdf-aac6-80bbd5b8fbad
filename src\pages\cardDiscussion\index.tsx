import { User } from "lucide-react"
import React, { useEffect, useRef, useState } from "react"
import { useAutoScrollToBottom } from "@/pages/hooks/useAutoScrollToBottom"
import ModalComponent from "./components/ModalComponent"
import { Button } from "antd"

const strategyChats = [
  {
    id: 1,
    name: "张三",
    avatar: "/avatars/avatar1.png",
    content: "我建议我们先出攻击卡压制对方, 然后根据对方的反应再做调整, 阿森纳大数据的你哈快速导航",
  },
  {
    id: 2,
    name: "李四",
    avatar: "/avatars/avatar2.png",
    content: "不妥，我认为可以先防守，观察对方动向。",
  },
  {
    id: 3,
    name: "王五",
    avatar: "/avatars/avatar3.png",
    content: "我同意李四的意见，稳住更重要。",
  },
  {
    id: 4,
    name: "赵六",
    avatar: "/avatars/avatar4.png",
    content: "我觉得我们应该先出一张强力的卡，直接秒杀对方。",
  },
  {
    id: 5,
    name: "钱七",
    avatar: "/avatars/avatar5.png",
    content: "我觉得我们应该先出一张强力的卡，直接秒杀对方。",
  },
  {
    id: 6,
    name: "孙八",
    avatar: "/avatars/avatar6.png",
    content: "我觉得我们应该先出一张强力的卡，直接秒杀对方。",
  },
]

const decisionSummary = [
  {
    id: 1,
    name: "决策者A",
    avatar: "/avatars/decider.png",
    content: "综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。",
  },
  {
    id: 2,
    name: "决策者A",
    avatar: "/avatars/decider.png",
    content: "下一轮将调整为攻守结合，提高灵活性。",
  },
  {
    id: 3,
    name: "决策者A",
    avatar: "/avatars/decider.png",
    content:
      "下一轮将调整为攻守结合，提高灵活性。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。",
  },
  {
    id: 4,
    name: "决策者A",
    avatar: "/avatars/decider.png",
    content:
      "下一轮将调整为攻守结合，提高灵活性。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。综合大家的意见，我们决定先采用防守策略，等待时机反击, 然后再出攻击卡压制对方。",
  },
]

type Message = {
  id: number
  name: string
  avatar: string
  content: string
}

const ChatBubble = ({
  name,
  //   avatar,
  content,
}: {
  name: string
  avatar?: string
  content: string
}) => (
  <div className="mb-4 flex py-2 text-white">
    <div className="mb-2 flex space-x-2">
      {/* <img src={avatar} alt={name} className="h-8 w-8 rounded-full object-cover" /> */}
      <User className="h-16 w-16" />
    </div>
    <div className="ml-2 flex flex-col">
      <span className="text-lg font-semibold">{name}</span>
      <span className="flex min-h-14 items-center whitespace-pre-wrap rounded-xl border border-solid border-[#295DB0] bg-[#ffffff1a] p-4 px-4 text-base leading-relaxed">
        {content}
      </span>
    </div>
  </div>
)

const CardDiscussion = () => {
  const [streamedStrategy, setStreamedStrategy] = useState<Message[]>([])
  const [typedContent, setTypedContent] = useState<string>("")
  const [currentIndex, setCurrentIndex] = useState<number>(0)
  const [charIndex, setCharIndex] = useState<number>(0)
  const [isTyping, setIsTyping] = useState<boolean>(false)
  const [showDecision, setShowDecision] = useState(false)
  const [decisionIndex, setDecisionIndex] = useState(0)
  const [typedDecision, setTypedDecision] = useState<string>("")
  const [decisionMessages, setDecisionMessages] = useState<Message[]>([])
  const [showModel, setShowModel] = useState(false)

  const msgContainerRef = useRef<HTMLDivElement>(null)
  useAutoScrollToBottom(msgContainerRef as React.RefObject<HTMLDivElement>, true, [
    streamedStrategy,
    isTyping,
  ])
  const decisionMsgContainerRef = useRef<HTMLDivElement>(null)
  useAutoScrollToBottom(decisionMsgContainerRef as React.RefObject<HTMLDivElement>, true, [
    decisionMessages,
    typedDecision,
  ])

  // 打字机效果 for 出牌讨论
  useEffect(() => {
    if (currentIndex < strategyChats.length) {
      if (!isTyping) {
        setIsTyping(true)
        setTypedContent("")
        setCharIndex(0)
      }

      const currentMessage = strategyChats[currentIndex]
      if (charIndex < currentMessage.content.length) {
        const timer = setTimeout(() => {
          setTypedContent(prev => prev + currentMessage.content[charIndex])
          setCharIndex(prev => prev + 1)
        }, 50)
        return () => clearTimeout(timer)
      } else {
        const timer = setTimeout(() => {
          setStreamedStrategy(prev => [...prev, { ...currentMessage, content: typedContent }])
          setCurrentIndex(prev => prev + 1)
          setIsTyping(false)
        }, 500)
        return () => clearTimeout(timer)
      }
    } else {
      // 全部讨论完成后延时展示总结
      const timer = setTimeout(() => {
        setShowDecision(true)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [charIndex, currentIndex, isTyping, typedContent])

  // 打字机效果 for 决策席位总结
  useEffect(() => {
    if (!showDecision || decisionIndex >= decisionSummary.length) return

    const current = decisionSummary[decisionIndex]
    if (typedDecision.length < current.content.length) {
      const timer = setTimeout(() => {
        setTypedDecision(prev => prev + current.content[typedDecision.length])
      }, 50)
      return () => clearTimeout(timer)
    } else {
      const timer = setTimeout(() => {
        setDecisionMessages(prev => [...prev, { ...current, content: typedDecision }])
        setTypedDecision("")
        setDecisionIndex(prev => prev + 1)
      }, 600)
      return () => clearTimeout(timer)
    }
  }, [typedDecision, decisionIndex, showDecision])

  return (
    <div>
      {/* 卡牌展示 */}
      <div className="mx-auto flex h-[200px] w-full flex-col">
        <div className="flex h-[60px] w-full items-center rounded-t-xl bg-[#2657A4] px-4 py-2 text-white">
          <span className="ml-4 text-[22px] font-bold">已生效卡牌</span>
        </div>
        <div className="flex flex-1 flex-col justify-center bg-[#132B51]">
          <div className="flex items-center space-x-4 px-8">
            <div className="flex items-center space-x-4 rounded-xl bg-[#4B5882]">
              <span className="flex h-[100px] min-w-[200px] items-center justify-center rounded-xl bg-[#679AF5] px-14 text-5xl font-bold">
                xx
              </span>
              <span className="text-5xl font-bold text-white">VS</span>
              <span className="flex h-[100px] min-w-[200px] items-center justify-center rounded-xl bg-[#679AF5] px-14 text-5xl font-bold">
                xx
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 讨论区 + 总结区 */}
      <div className="mt-6 flex">
        {/* 出牌策略讨论 */}
        <div className="flex h-[700px] w-3/5 flex-col overflow-y-auto">
          <div className="flex h-[80px] w-full items-center rounded-t-xl bg-[#2657A4] px-4 py-2 text-white">
            <span className="ml-4 text-[22px] font-bold">出牌策略讨论</span>
          </div>
          <div
            ref={msgContainerRef}
            className="flex flex-1 flex-col overflow-y-auto bg-[#132B51] px-8 py-4 pr-44"
          >
            {streamedStrategy.map(msg => (
              <ChatBubble key={msg.id} {...msg} />
            ))}
            {isTyping && (
              <ChatBubble
                name={strategyChats[currentIndex].name}
                avatar={strategyChats[currentIndex].avatar}
                content={typedContent}
              />
            )}
          </div>
        </div>

        {/* 决策席位总结 */}
        <div className="ml-6 flex h-[700px] w-2/5 flex-col">
          <div className="flex h-[80px] w-full flex-shrink-0 items-center rounded-t-xl bg-[#2657A4] px-4 py-2 text-white">
            <span className="ml-4 text-[22px] font-bold">决策席位总结</span>
          </div>
          <div className="relative flex flex-1 bg-[#132B51]">
            <div
              ref={decisionMsgContainerRef}
              className="flex h-[620px] flex-1 flex-col overflow-y-auto bg-[#132B51] px-8 py-4 pb-16"
            >
              {!showDecision ? (
                <div className="flex h-full flex-col items-center justify-center text-center text-white">
                  <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-400"></div>
                  <p className="mt-4">等待讨论完成中...</p>
                </div>
              ) : (
                <>
                  {decisionMessages.map(msg => (
                    <ChatBubble key={msg.id} {...msg} />
                  ))}
                  {typedDecision && (
                    <ChatBubble
                      name={decisionSummary[decisionIndex].name}
                      // avatar={decisionSummary[decisionIndex].avatar}
                      content={typedDecision}
                    />
                  )}
                </>
              )}
            </div>
            <div className="absolute bottom-0 left-0 right-0 flex h-20 items-center justify-center bg-[#132B51]">
              <Button
                type="primary"
                disabled={!showDecision || decisionIndex < decisionSummary.length}
                onClick={() => {
                  setShowModel(true)
                }}
                className="flex h-[40px] w-[180px] items-center justify-center rounded-lg bg-[#2A81FF] text-lg font-medium text-white transition-all duration-300 hover:bg-[#2f86ff]"
              >
                生成决策卡牌
              </Button>
            </div>
          </div>
        </div>
      </div>
      <ModalComponent
        open={showModel}
        onClose={() => {
          setShowModel(false)
        }}
      />
    </div>
  )
}

export default CardDiscussion
