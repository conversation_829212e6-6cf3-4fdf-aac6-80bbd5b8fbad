import { baseResponse, httpClient } from '@/utils/request';
export interface DeductionProcess{
    process_id: number;
    seat_id: number;
    character_name: string;
    start_time: string;
    progress_value: number;
    redundant_field: string;
}

//获取列表
export const getDeductionProcessList = async (): Promise<DeductionProcess> => {
    return httpClient.get<DeductionProcess>('/deduction_process/deduction_process/');
};
