import React from 'react'

const ScaleTest: React.FC = () => {
  return (
    <div className="px-28">
      <div className="flex h-[900px] w-full flex-col items-center justify-center rounded-[20px] bg-[#132B51] px-48">
        <div className="text-[80px] font-bold text-white mb-8">缩放测试页面</div>
        
        <div className="grid grid-cols-3 gap-8 w-full">
          <div className="h-[200px] bg-[#14438E] rounded-[20px] flex items-center justify-center">
            <span className="text-[24px] font-bold text-white">卡片 1</span>
          </div>
          <div className="h-[200px] bg-[#14438E] rounded-[20px] flex items-center justify-center">
            <span className="text-[24px] font-bold text-white">卡片 2</span>
          </div>
          <div className="h-[200px] bg-[#14438E] rounded-[20px] flex items-center justify-center">
            <span className="text-[24px] font-bold text-white">卡片 3</span>
          </div>
        </div>
        
        <div className="mt-16 text-[20px] text-gray-300 text-center">
          <p>这个页面应该在任何屏幕尺寸下都保持1920x1080的比例</p>
          <p>当前视口信息:</p>
          <p>宽度: {typeof window !== 'undefined' ? window.innerWidth : 'N/A'}px</p>
          <p>高度: {typeof window !== 'undefined' ? window.innerHeight : 'N/A'}px</p>
          <p>比例: {typeof window !== 'undefined' ? (window.innerWidth / window.innerHeight).toFixed(2) : 'N/A'}</p>
        </div>
        
        <div className="mt-8 flex gap-4">
          <div className="w-[150px] h-[60px] bg-[#2A81FF] rounded-lg flex items-center justify-center text-white font-bold">
            按钮 1
          </div>
          <div className="w-[150px] h-[60px] bg-[#2A81FF] rounded-lg flex items-center justify-center text-white font-bold">
            按钮 2
          </div>
        </div>
      </div>
    </div>
  )
}

export default ScaleTest
