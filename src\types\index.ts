// 人物卡牌类型
export interface Character {
  character_id?: number;
  character_name: string;
  avatar?: string;
  character_description: string;
  openness: number;
  neuroticism: number;
  conscientiousness: number;
  agreeableness: number;
  extraversion: number;
}

// 讨论类型
export interface Discussion {
  id: string;
  characterId: string;
  title: string;
  content: string;
  author: string;
  replies: Reply[];
  createdAt: string;
  updatedAt: string;
}

export interface Reply {
  id: string;
  content: string;
  author: string;
  createdAt: string;
}

// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
}

// 职位数据类型（从API返回）
export interface PositionData {
  position_id: number;
  decision_making_style: string;
  position_title: string;
  position_focus: string;
}

// 席位信息类型
export interface SeatInfo {
  position_focus: string; // 用户关注点
  position_title: string; // 职位
  decision_making_style: string; // 决策方式
  position_id?: number; // 职位ID（如果是从API选择的）
  isCustomPosition?: boolean; // 是否为自定义职位
}

// 席位类型
export interface Seat {
  id: string;
  position: number; // 席位位置 (0-8)
  type: 'decision' | 'general'; // 决策席位或一般席位
  character?: Character; // 分配的角色
  position_title?: string; // 职位
  seatInfo?: SeatInfo; // 席位信息
}

// 职位选项
export interface PositionOption {
  value: string;
  label: string;
}

// 席位配置
export interface SeatConfiguration {
  totalSeats: number; // 总席位数 (1-9)
  seats: Seat[];
}

// API响应类型
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}