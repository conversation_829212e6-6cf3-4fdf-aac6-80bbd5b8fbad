import { Button, Pagination, Progress, Table, TablePaginationConfig } from "antd"
import "./index.css"
import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { DeductionProcess, getDeductionProcessList } from "@/api/deductionProcess"

const DeductionProcessList = () => {
  interface TableParams {
    pagination?: TablePaginationConfig
  }
  const navigate = useNavigate()

  const [loading, setLoading] = useState(false)
  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 7, // 每页最多7条数据
      pageSizeOptions: ["7", "14", "21", "28"], // 调整每页数量选项
    },
  })
  const [total, setTotal] = useState(0)
  const [data, setData] = useState<DeductionProcess[]>([])
  const currentPage = tableParams.pagination?.current || 1
  const pageSize = tableParams.pagination?.pageSize || 7

  // 切片出当前页展示的数据
  const paginatedData = data.slice((currentPage - 1) * pageSize, currentPage * pageSize)

  // 处理分页、排序、筛选变化
  const handleTableChange = (pagination: TablePaginationConfig) => {
    setTableParams({
      pagination,
    })
  }

  const columns = [
    {
      title: "任务名称",
      dataIndex: "character_name",
      key: "process_id",
      width: 200,
    },
    {
      title: "开始时间",
      dataIndex: "start_time",
      key: "start_time",
      width: 180,
    },
    {
      title: "进度",
      dataIndex: "progress_value",
      key: "progress_value",
      width: 220,
      render: (percent: number) => (
        <div className="flex items-center">
          <span className="mr-3">{percent}%</span>
          <Progress
            percent={percent}
            size={[200, 12]}
            showInfo={false}
            trailColor="rgba(255, 255, 255, 0.1)"
            status={percent === 0 ? "normal" : percent === 100 ? "success" : "active"}
          />
        </div>
      ),
    },
    {
      title: "操作",
      key: "action",
      width: 100,
      render: (_, record: DeductionProcess) => (
        <Button
          type="primary"
          className="w-24 rounded-xl bg-[#2A81FF]"
          onClick={() => {
            navigate(`/characters/${record.seat_id}`)
          }}
        >
          进入
        </Button>
      ),
    },
  ]

  const fetchData = async () => {
    try {
      setLoading(true)
      await getDeductionProcessList().then(res => {
        if (res.code === 200) {
          setData(res.data)
          setTotal(res.data.length)
        }
        setLoading(false)
      })
    } catch (error) {
      console.error("Failed to fetch data:", error)
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  return (
    <div>
      <Table
        columns={columns}
        dataSource={paginatedData}
        loading={loading}
        className="processTable min-h-[600px]"
        pagination={false}
        rowKey="process_id"
      />
      <div className="mt-8 flex items-center justify-between">
        <div>共 {total} 条记录</div>
        <Pagination
          className="process-page"
          align="center"
          current={tableParams.pagination?.current || 1}
          pageSize={tableParams.pagination?.pageSize || 7}
          total={total}
          showSizeChanger
          showQuickJumper
          pageSizeOptions={["7", "14", "21", "28"]} // 每页数量选项
          onChange={(page, pageSize) => {
            handleTableChange({
              ...tableParams.pagination,
              current: page,
              pageSize,
            })
          }}
          onShowSizeChange={(current, size) => {
            handleTableChange({
              ...tableParams.pagination,
              current,
              pageSize: size,
            })
          }}
          locale={{
            items_per_page: "条/页",
            jump_to: "跳转至",
            jump_to_confirm: "确定",
            page: "页",
          }}
        />
      </div>
    </div>
  )
}

export default DeductionProcessList
