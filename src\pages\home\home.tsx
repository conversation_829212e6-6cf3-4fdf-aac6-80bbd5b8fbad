import React from "react"
import { useNavigate } from "react-router-dom"

const Home: React.FC = () => {
  const navigate = useNavigate()
  return (
    <div className="px-28">
      <div className="flex h-[900px] w-full flex-col items-center justify-center rounded-[20px] bg-[#132B51] px-48">
        <div className="text-[80px] font-bold text-white">智子系统</div>
        <div className="mt-64 flex w-full items-center justify-between gap-4 text-[32px] font-black">
          <div
            className="flex h-36 w-96 cursor-pointer items-center justify-center rounded-[10px] bg-[#14438E] px-2 py-1"
            onClick={() => navigate("/create")}
          >
            新增任务卡牌
          </div>
          <div
            className="flex h-36 w-96 cursor-pointer items-center justify-center rounded-[10px] bg-[#14438E] px-2 py-1"
            onClick={() => navigate("/deductionProcess")}
          >
            智能蓝方出牌
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home
