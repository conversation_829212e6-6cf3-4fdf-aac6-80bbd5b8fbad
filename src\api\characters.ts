import { httpClient } from '@/utils/request';
import { Character } from '@/types';

const baseURL = '/card_character';

export interface CharacterPosition {
    decision_making_style: string;
    position_title: string;
    position_focus: string;
    position_id?: number;
}

//创建卡牌职位记录
export const createCharacterPosition = async (data: any): Promise<any> => {
    return httpClient.post<Character>(`/card_position/card_position/`, data);
};

//获取所有卡牌角色记录
export const getCharacterRole = async (): Promise<any> => {
    return httpClient.get<Character>(`${baseURL}/card_character/`);
};

//获取所有卡牌职位记录
export const getCharacterPositions = async (): Promise<any> => {
    return httpClient.get<Character>(`/card_position/card_position/`);
};

//更新卡牌职位记录
export const updateCharacterPosition = async ( data: CharacterPosition): Promise<any> => {
    return httpClient.post<CharacterPosition>(`/card_position/card_position/`, data);
};

export const getCharacters = async (): Promise<Character[]> => {
    return httpClient.get<Character[]>('/characters');
};
