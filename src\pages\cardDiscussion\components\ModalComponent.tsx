import React from "react"
import { Modal, Input, Select, But<PERSON>, Tabs, Table } from "antd"
import styles from "./ModalComponent.module.css"

const { TextArea } = Input
// const { Option } = Select

const columns = [
  {
    title: "指标名称",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "对方当前值",
    dataIndex: "current",
    key: "current",
  },
  {
    title: "对方决策值",
    dataIndex: "decision",
    key: "decision",
  },
  {
    title: "对方结果值",
    dataIndex: "result",
    key: "result",
  },
  {
    title: "操作",
    dataIndex: "action",
    key: "action",
  },
]

const dataSource: readonly [] | undefined = [] // 可按需填充数据

const ModalComponent: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
  return (
    <Modal open={open} footer={null} closable={false} width={1440} className={styles.modalWrapper}>
      <div className={styles.container}>
        <div className={styles.topRow}>
          <Select className={styles.selectBox} placeholder="请选择" />
          <Input className={styles.inputBox} placeholder="请输入" />
          <div className={styles.vs}>VS</div>
          <Select className={styles.selectBox} placeholder="请选择" />
        </div>
        <div className="flex w-full">
          <div className="mr-8 flex w-[400px] flex-col">
            <div className={styles.section}>
              <div className={styles.label}>概述:</div>
              <TextArea rows={4} placeholder="请输入概述" className={styles.textArea} />
            </div>

            <div className={styles.section}>
              <div className={styles.label}>收益:</div>
              <TextArea rows={4} placeholder="请输入收益" className={styles.textArea} />
            </div>

            <div className={styles.section}>
              <div className={styles.label}>代价:</div>
              <TextArea rows={4} placeholder="请输入代价" className={styles.textArea} />
            </div>

            <div className={styles.section}>
              <div className={styles.label}>风险:</div>
              <TextArea rows={4} placeholder="请输入风险" className={styles.textArea} />
            </div>
          </div>
          <div className="flex-1">
            <Tabs defaultActiveKey="1" className={styles.tabs}>
              <Tabs.TabPane tab="指标变化" key="1">
                <div className={styles.indicatorRow}>
                  <Input placeholder="指标变化" className={styles.indicatorInput} />
                  <Button className={styles.addButton}>添加指标</Button>
                </div>
                <Input placeholder="对方：" className={styles.opponentInput} />

                <Table
                  columns={columns}
                  dataSource={dataSource}
                  pagination={false}
                  className={styles.customTable}
                />
              </Tabs.TabPane>
              <Tabs.TabPane tab="资源变化" key="4" />
              <Tabs.TabPane tab="XX变化" key="3" />
            </Tabs>
          </div>
        </div>

        <div className={styles.footer}>
          <div className="flex gap-4">
            <Button className={styles.btn} type="primary">
              简设
            </Button>
            <Button className={styles.btn} type="primary">
              执行地点
            </Button>
          </div>

          <div className={styles.btnGroup}>
            <Button className={styles.btn} type="primary" onClick={onClose}>
              关闭
            </Button>
            <Button className={styles.btn} type="primary">
              保存
            </Button>
            <Button className={styles.btn} type="primary">
              提交
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default ModalComponent
