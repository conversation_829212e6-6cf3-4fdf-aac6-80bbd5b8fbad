import React, { useState, useEffect } from 'react'

interface ScaleInfo {
  windowWidth: number
  windowHeight: number
  scaleX: number
  scaleY: number
  finalScale: number
  aspectRatio: string
}

const ScaleDebugger: React.FC = () => {
  const [scaleInfo, setScaleInfo] = useState<ScaleInfo>({
    windowWidth: 0,
    windowHeight: 0,
    scaleX: 1,
    scaleY: 1,
    finalScale: 1,
    aspectRatio: '16:9'
  })
  
  const [isVisible, setIsVisible] = useState(false)

  const updateScaleInfo = () => {
    if (typeof window !== 'undefined') {
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight
      const scaleX = windowWidth / 1920
      const scaleY = windowHeight / 1080
      const finalScale = Math.min(scaleX, scaleY)
      
      // 计算宽高比
      const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b)
      const divisor = gcd(windowWidth, windowHeight)
      const aspectRatio = `${windowWidth / divisor}:${windowHeight / divisor}`
      
      setScaleInfo({
        windowWidth,
        windowHeight,
        scaleX,
        scaleY,
        finalScale,
        aspectRatio
      })
    }
  }

  useEffect(() => {
    updateScaleInfo()
    window.addEventListener('resize', updateScaleInfo)
    return () => window.removeEventListener('resize', updateScaleInfo)
  }, [])

  // 键盘快捷键切换显示
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'd') {
        e.preventDefault()
        setIsVisible(!isVisible)
      }
    }
    
    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [isVisible])

  if (!isVisible) {
    return (
      <div 
        className="fixed bottom-4 right-4 z-50 bg-black/50 text-white px-2 py-1 rounded text-xs cursor-pointer"
        onClick={() => setIsVisible(true)}
      >
        Debug (Ctrl+D)
      </div>
    )
  }

  return (
    <div className="fixed top-4 right-4 z-50 bg-black/80 text-white p-4 rounded-lg text-sm font-mono">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">缩放调试信息</h3>
        <button 
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>
      
      <div className="space-y-1">
        <div>窗口尺寸: {scaleInfo.windowWidth} × {scaleInfo.windowHeight}</div>
        <div>宽高比: {scaleInfo.aspectRatio}</div>
        <div>X轴缩放: {scaleInfo.scaleX.toFixed(3)}</div>
        <div>Y轴缩放: {scaleInfo.scaleY.toFixed(3)}</div>
        <div>最终缩放: {scaleInfo.finalScale.toFixed(3)}</div>
        <div>缩放百分比: {(scaleInfo.finalScale * 100).toFixed(1)}%</div>
      </div>
      
      <div className="mt-3 pt-2 border-t border-gray-600">
        <div className="text-xs text-gray-400">
          设计尺寸: 1920 × 1080 (16:9)
        </div>
        <div className="text-xs text-gray-400">
          按 Ctrl+D 切换显示
        </div>
      </div>
      
      {scaleInfo.finalScale < 0.6 && (
        <div className="mt-2 p-2 bg-yellow-600/20 border border-yellow-600 rounded text-xs">
          ⚠️ 缩放比例较小，可能影响可读性
        </div>
      )}
      
      {scaleInfo.aspectRatio !== '16:9' && (
        <div className="mt-2 p-2 bg-blue-600/20 border border-blue-600 rounded text-xs">
          ℹ️ 非16:9比例，会有留白区域
        </div>
      )}
    </div>
  )
}

export default ScaleDebugger
