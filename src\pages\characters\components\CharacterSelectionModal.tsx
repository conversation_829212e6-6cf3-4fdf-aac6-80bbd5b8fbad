import React, { useState, useMemo, useEffect } from "react"
import { Search, User } from "lucide-react"
import { Character } from "@/types"
import BaseModal from "@/components/modal"
import { getCharacterRole } from "@/api/characters"

interface CharacterSelectionModalProps {
  isOpen: boolean
  onConfirm: (character: Character) => void
  onClose: () => void
}

const CharacterSelectionModal: React.FC<CharacterSelectionModalProps> = ({
  isOpen,
  onConfirm,
  onClose,
}) => {
  const [searchTerm, setSearchTerm] = useState("")
  const [characters, setCharacters] = useState<Character[]>([])
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null)

  const filteredCharacters = useMemo(() => {
    if (!searchTerm.trim()) return characters
    return characters.filter(c => c.character_name.toLowerCase().includes(searchTerm.toLowerCase()))
  }, [searchTerm, characters])

  const getData = async () => {
    try {
      const res = await getCharacterRole()
      if (res.code === 200) {
        setCharacters(res.data)
      }
    } catch (error) {
      console.error("Failed to fetch data:", error)
    }
  }

  const resetState = () => {
    setSearchTerm("")
    setSelectedCharacter(null)
  }

  const handleConfirm = () => {
    if (selectedCharacter) {
      onConfirm(selectedCharacter)
      resetState()
    }
  }

  const handleClose = () => {
    resetState()
    onClose()
  }

  const handleCharacterSelect = (character: Character) => {
    setSelectedCharacter(
      selectedCharacter?.character_id === character.character_id ? null : character
    )
  }

  const truncateText = (text: string, maxLength: number) => {
    return text.length > maxLength ? text.slice(0, maxLength) + "..." : text
  }
  useEffect(() => {
    if (isOpen) {
      getData()
    }
  }, [isOpen])

  return (
    <BaseModal
      isOpen={isOpen}
      title="选择角色"
      onConfirm={handleConfirm}
      onCancel={handleClose}
      confirmText="确认选择"
      cancelText="关闭"
      confirmDisabled={!selectedCharacter}
      showFooter={true}
      headerRightSlot={
        <div className="relative ml-auto w-full">
          <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="输入姓名搜索..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="w-full rounded-lg border border-gray-600 bg-primary-dark py-3 pl-10 pr-4 text-white transition-all duration-300 focus:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-400/30"
          />
        </div>
      }
    >
      {/* 角色列表 */}
      <div className="custom-scrollbar h-[33.5rem]">
        {filteredCharacters.length > 0 ? (
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            {filteredCharacters.map(character => (
              <div
                key={character.character_id}
                onClick={() => handleCharacterSelect(character)}
                className={`duration-400 h-[152px] cursor-pointer rounded-[20px] border-2 p-4 transition-all ${
                  selectedCharacter?.character_id === character.character_id
                    ? "scale-[1.02] border-blue-400 bg-blue-900/30 shadow-lg shadow-blue-500/20"
                    : "border-transparent hover:border-blue-300/70"
                }`}
                style={{ backgroundColor: "#14438E" }}
              >
                <div className="mb-3 flex items-center space-x-3">
                  <div className="h-12 w-12 overflow-hidden rounded-full bg-gray-600">
                    {character.avatar ? (
                      <img
                        src={character.avatar}
                        alt={character.character_name}
                        className="h-full w-full object-cover transition-transform duration-500 hover:scale-110"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center">
                        <User className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="truncate font-medium text-white">
                      {truncateText(character.character_name, 15)}
                    </h3>
                  </div>
                </div>
                <p className="line-clamp-3 text-sm text-gray-300">
                  {truncateText(character.character_description, 50)}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex h-full flex-col justify-center py-12 text-center">
            <User className="mx-auto mb-4 h-16 w-16 text-gray-500" />
            <p className="text-lg text-gray-400">未找到匹配的卡牌</p>
          </div>
        )}
      </div>
    </BaseModal>
  )
}

export default CharacterSelectionModal
