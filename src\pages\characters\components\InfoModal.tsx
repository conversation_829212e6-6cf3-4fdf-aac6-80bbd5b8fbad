import BaseModal from "@/components/modal"
import { Button, Divider, Form, Input, InputRef, message, Select, Space, Tooltip } from "antd"
import TextArea from "antd/es/input/TextArea"
import { CirclePlus } from "lucide-react"
import React, { useRef, useState, useEffect } from "react"
import {
  getCharacterPositions,
  createCharacterPosition,
  updateCharacterPosition,
} from "@/api/characters"
import { PositionData } from "@/types"

interface infoModalProps {
  isOpen: boolean
  onClose: () => void
  onInfoConfirm: (formData: FieldType) => void
  initialData?: FieldType
  isViewMode?: boolean
}
export type FieldType = {
  position_focus: string
  position_title: string
  decision_making_style: string
  position_id?: number
  isCustomPosition?: boolean
}

const InfoModal: React.FC<infoModalProps> = ({
  isOpen,
  onClose,
  onInfoConfirm,
  initialData,
  isViewMode = false,
}) => {
  const [form] = Form.useForm()
  const [name, setName] = useState("")
  const inputRef = useRef<InputRef>(null)
  const [positionData, setPositionData] = useState<PositionData[]>([])
  const [isAPIPostionSelected, setIsAPIPostionSelected] = useState(false)
  const [positionSelected, setPositionSelected] = useState(false)

  const addItem = (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault()
    if (name.trim()) {
      const exists = positionData.some(item => item.position_title === name.trim())
      if (!exists) {
        const newPosition: PositionData = {
          position_id: -1,
          position_title: name.trim(),
          decision_making_style: "",
          position_focus: "",
        }
        setPositionData(prev => [...prev, newPosition])
      }
      setName("")
      setTimeout(() => {
        inputRef.current?.focus()
      }, 0)
    }
  }

  const onNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value)
  }

  const getData = async () => {
    try {
      const res = await getCharacterPositions()
      if (res.code === 200) {
        setPositionData(res.data)
      } else {
        message.error(res.message || "获取职位数据失败")
      }
    } catch (error) {
      console.error("Failed to fetch data:", error)
      message.error("获取职位数据失败")
    }
  }

  useEffect(() => {
    if (isOpen) getData()
  }, [isOpen])

  useEffect(() => {
    if (isOpen && initialData) {
      form.setFieldsValue(initialData)
      setPositionSelected(true)
    }
  }, [isOpen, initialData, form])

  const handlePositionChange = (value: string) => {
    const found = positionData.find(p => p.position_title === value)
    setPositionSelected(true)

    if (found && found.position_id !== -1) {
      // 来自API职位
      setIsAPIPostionSelected(true)
      form.setFieldsValue({
        position_focus: found.position_focus || "",
        decision_making_style: found.decision_making_style || "",
      })
    } else {
      // 自定义职位
      setIsAPIPostionSelected(false)
      form.setFieldsValue({
        position_focus: "",
        decision_making_style: "",
      })
    }
  }

  const handleConfirm = async () => {
    try {
      const values = await form.validateFields()
      const selectedPosition = positionData.find(p => p.position_title === values.position_title)
      const finalData = { ...values }

      if (selectedPosition && selectedPosition.position_id !== -1) {
        finalData.position_id = selectedPosition.position_id
        finalData.isCustomPosition = false
        await updateCharacterPosition({
          position_id: selectedPosition.position_id,
          decision_making_style: values.decision_making_style,
          position_title: values.position_title,
          position_focus: values.position_focus,
        })
        message.success("保存成功")
      } else {
        finalData.isCustomPosition = true
        const createRes = await createCharacterPosition({
          decision_making_style: values.decision_making_style,
          position_title: values.position_title,
          position_focus: values.position_focus,
        })
        message.success("保存成功")
        if (createRes.code === 200) {
          finalData.position_id = createRes.data.position_id
        }
      }

      onInfoConfirm(finalData)
      resetState()
    } catch (errorInfo) {
      console.log("Validation Failed:", errorInfo)
    }
  }

  const handleClose = () => {
    onClose()
    resetState()
  }

  const resetState = () => {
    form.resetFields()
    setName("")
    setIsAPIPostionSelected(false)
    setPositionSelected(false)
    setPositionData(prev => prev.filter(item => item.position_id !== -1))
  }

  const shouldDisableFields = !positionSelected

  return (
    <BaseModal
      isOpen={isOpen}
      title={isViewMode ? "查看信息" : "信息填写"}
      onConfirm={handleConfirm}
      onCancel={handleClose}
      confirmText={isViewMode ? "确定" : "保存"}
      cancelText="关闭"
      width={800}
      height={540}
      showFooter={true}
    >
      <div className="custom-scrollbar max-h-[33.5rem]">
        <Form form={form} layout="vertical" size="large">
          <Form.Item
            label="职位"
            name="position_title"
            rules={[{ required: true, message: "请选择职位！" }]}
          >
            <Select
              placeholder="请选择职位"
              showSearch
              onChange={handlePositionChange}
              popupRender={menu => (
                <>
                  {menu}
                  <Divider style={{ margin: "8px 0" }} />
                  <Space style={{ padding: "0 8px 4px" }}>
                    <Input
                      placeholder="请输入"
                      ref={inputRef}
                      value={name}
                      onChange={onNameChange}
                      onKeyDown={e => e.stopPropagation()}
                    />
                    <Button type="primary" icon={<CirclePlus />} onClick={addItem}>
                      添加职位
                    </Button>
                  </Space>
                </>
              )}
              options={positionData.map(item => ({
                label: item.position_title,
                value: item.position_title,
              }))}
            />
          </Form.Item>

          <Tooltip
            title={shouldDisableFields ? "请选择职位" : ""}
            placement="topLeft"
            open={shouldDisableFields ? undefined : false}
          >
            <Form.Item
              label="用户关注点"
              name="position_focus"
              rules={[{ required: true, message: "请输入用户关注点！" }]}
            >
              <TextArea placeholder="请输入用户关注点" disabled={shouldDisableFields} />
            </Form.Item>
          </Tooltip>

          <Tooltip
            title={shouldDisableFields ? "请选择职位" : ""}
            placement="topLeft"
            open={shouldDisableFields ? undefined : false}
          >
            <Form.Item
              label="决策方式"
              name="decision_making_style"
              rules={[{ required: true, message: "请输入决策方式！" }]}
            >
              <Input placeholder="请输入决策方式" disabled={shouldDisableFields} />
            </Form.Item>
          </Tooltip>
        </Form>
      </div>
    </BaseModal>
  )
}

export default InfoModal
