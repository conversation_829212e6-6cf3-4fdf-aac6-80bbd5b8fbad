import React, { useState, useEffect } from "react"
import { <PERSON> } from "react-router-dom"
import { MessageSquare, User, Clock } from "lucide-react"
import { Discussion } from "@/types"
import { mockDiscussions } from "./mock"

const Discussions: React.FC = () => {
  const [discussions, setDiscussions] = useState<Discussion[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchDiscussions()
  }, [])

  const fetchDiscussions = async () => {
    try {
      setLoading(true)
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 800))
      setDiscussions(mockDiscussions)
    } catch (error) {
      console.error("Failed to fetch discussions:", error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN")
  }

  if (loading) {
    return (
      <div className="text-center text-white">
        <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-400"></div>
        <p className="mt-4">加载中...</p>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">卡牌讨论</h1>
        <Link
          to="/discussions/new"
          className="rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
        >
          发起讨论
        </Link>
      </div>

      <div className="space-y-4">
        {discussions.map(discussion => (
          <div
            key={discussion.id}
            className="rounded-lg bg-primary-dark p-6 transition-colors hover:bg-primary-light"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <Link
                  to={`/discussions/${discussion.id}`}
                  className="text-xl font-semibold text-white transition-colors hover:text-blue-400"
                >
                  {discussion.title}
                </Link>
                <p className="mt-2 line-clamp-2 text-gray-300">{discussion.content}</p>

                <div className="mt-4 flex items-center space-x-4 text-sm text-gray-400">
                  <div className="flex items-center space-x-1">
                    <User className="h-4 w-4" />
                    <span>{discussion.author}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{formatDate(discussion.createdAt)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageSquare className="h-4 w-4" />
                    <span>{discussion.replies.length} 回复</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {discussions.length === 0 && (
        <div className="mt-12 text-center text-gray-400">
          <MessageSquare className="mx-auto mb-4 h-16 w-16 opacity-50" />
          <p>暂无讨论，快来发起第一个讨论吧！</p>
        </div>
      )}
    </div>
  )
}

export default Discussions
